<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OfferController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['error' => 'Non authentifié'], 401);
        }

        $query = Offer::where('company_id', $user->id);

        // Apply filters
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Apply sorting
        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'date':
                    $query->orderBy('created_at', 'desc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $offers = $query->get();

        return response()->json(['offers' => $offers]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['error' => 'Non authentifié'], 401);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'budget' => 'required|numeric',
            'deadline' => 'required|date',
            'status' => 'required|in:pending,active'
        ]);

        $offer = Offer::create([
            'company_id' => $user->id,
            'name' => $validated['name'],
            'budget' => $validated['budget'],
            'deadline' => $validated['deadline'],
            'status' => $validated['status'],
            'views' => 0,
            'shares' => 0,
            'interactions' => 0
        ]);

        return response()->json(['message' => 'Offre créée avec succès', 'offer' => $offer]);
    }
}
