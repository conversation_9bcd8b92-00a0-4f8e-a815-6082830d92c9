<?php

namespace App\Http\Controllers;

use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OfferController extends Controller
{
    public function store(Request $request)
{
    $user = Auth::guard('api')->user();
    if (!$user) {
        return response()->json(['error' => 'Non authentifié'], 401);
    }

    $validated = $request->validate([
        'company_id' => 'required|exists:companies,id',
        'name' => 'required|string|max:255',
        'budget' => 'required|numeric',
        'deadline' => 'required|date',
        'status' => 'required|in:pending,active'
    ]);

    $offer = Offer::create([
        'company_id' => $user->id,
        'name' => $validated['name'],
        'budget' => $validated['budget'],
        'deadline' => $validated['deadline'],
        'status' => $validated['status'],
        'views' => 0,
        'shares' => 0,
        'interactions' => 0
    ]);

    return response()->json(['message' => 'Offre créée avec succès', 'offer' => $offer]);
}
}