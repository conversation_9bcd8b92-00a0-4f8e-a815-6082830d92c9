<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\OfferController;

Route::post('/pre-register', [AuthController::class, 'preRegister']);
Route::post('/process-payment', [AuthController::class, 'processPayment']);
Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
Route::post('/login', [AuthController::class, 'login']);

// Routes protégées par Sanctum
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/profile-setup', [AuthController::class, 'profileSetup']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::get('/offers', [OfferController::class, 'index']);
    Route::post('/offers', [OfferController::class, 'store']);
});