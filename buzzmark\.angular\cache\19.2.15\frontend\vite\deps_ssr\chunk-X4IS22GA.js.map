{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/platform.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/coercion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  _platformId = inject(PLATFORM_ID);\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n  /** Whether the current platform is Apple iOS. */\n  IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  constructor() {}\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass PlatformModule {\n  static ɵfac = function PlatformModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PlatformModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PlatformModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n\n/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nvar RtlScrollAxisType;\n(function (RtlScrollAxisType) {\n  /**\n   * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"NORMAL\"] = 0] = \"NORMAL\";\n  /**\n   * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"NEGATED\"] = 1] = \"NEGATED\";\n  /**\n   * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"INVERTED\"] = 2] = \"INVERTED\";\n})(RtlScrollAxisType || (RtlScrollAxisType = {}));\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    }\n    // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n    if ('scrollBehavior' in document.documentElement.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction = Element.prototype.scrollTo;\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n  return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return RtlScrollAxisType.NORMAL;\n  }\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n    rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n    // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType = scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n    }\n    scrollContainer.remove();\n  }\n  return rtlScrollAxisType;\n}\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\n\n/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Jest.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n  return (\n    // @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ ||\n    // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine ||\n    // @ts-ignore\n    typeof jest !== 'undefined' && !!jest ||\n    // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha\n  );\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Platform, PlatformModule, RtlScrollAxisType, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior };\n", "import { ElementRef } from '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return typeof value === 'string' ? value : `${value}px`;\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n  const result = [];\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n  return result;\n}\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAMA,IAAI;AAMJ,IAAI;AACF,uBAAqB,OAAO,SAAS,eAAe,KAAK;AAC3D,QAAQ;AACN,uBAAqB;AACvB;AAKA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc,OAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,YAAY,KAAK,cAAc,kBAAkB,KAAK,WAAW,IAAI,OAAO,aAAa,YAAY,CAAC,CAAC;AAAA;AAAA,EAEvG,OAAO,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS;AAAA;AAAA,EAE3D,UAAU,KAAK,aAAa,kBAAkB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAGtE,QAAQ,KAAK,aAAa,CAAC,EAAE,OAAO,UAAU,uBAAuB,OAAO,QAAQ,eAAe,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA,EAIvH,SAAS,KAAK,aAAa,eAAe,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA,EAE1G,MAAM,KAAK,aAAa,mBAAmB,KAAK,UAAU,SAAS,KAAK,EAAE,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxF,UAAU,KAAK,aAAa,uBAAuB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAG3E,UAAU,KAAK,aAAa,WAAW,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,SAAS,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS,KAAK,KAAK;AAAA,EACvE,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,UAAS;AAAA,IAClB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAEJ,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAM;AAE/M,SAAS,yBAAyB;AAEhC,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC7C,0BAAsB,IAAI,IAAI,mBAAmB;AACjD,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,SAAS,cAAc,OAAO;AACrD,wBAAsB,IAAI,IAAI,oBAAoB,OAAO,WAAS;AAChE,qBAAiB,aAAa,QAAQ,KAAK;AAC3C,WAAO,iBAAiB,SAAS;AAAA,EACnC,CAAC,CAAC;AACF,SAAO;AACT;AAGA,IAAI;AAKJ,SAAS,gCAAgC;AACvC,MAAI,yBAAyB,QAAQ,OAAO,WAAW,aAAa;AAClE,QAAI;AACF,aAAO,iBAAiB,QAAQ,MAAM,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,QACzE,KAAK,MAAM,wBAAwB;AAAA,MACrC,CAAC,CAAC;AAAA,IACJ,UAAE;AACA,8BAAwB,yBAAyB;AAAA,IACnD;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,gCAAgC,SAAS;AAChD,SAAO,8BAA8B,IAAI,UAAU,CAAC,CAAC,QAAQ;AAC/D;AAGA,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAK5B,EAAAA,mBAAkBA,mBAAkB,QAAQ,IAAI,CAAC,IAAI;AAKrD,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AAKtD,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AACzD,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,IAAI;AAEJ,IAAI;AAEJ,SAAS,yBAAyB;AAChC,MAAI,2BAA2B,MAAM;AAGnC,QAAI,OAAO,aAAa,YAAY,CAAC,YAAY,OAAO,YAAY,cAAc,CAAC,SAAS;AAC1F,gCAA0B;AAC1B,aAAO;AAAA,IACT;AAEA,QAAI,oBAAoB,SAAS,gBAAgB,OAAO;AACtD,gCAA0B;AAAA,IAC5B,OAAO;AAGL,YAAM,mBAAmB,QAAQ,UAAU;AAC3C,UAAI,kBAAkB;AAKpB,kCAA0B,CAAC,4BAA4B,KAAK,iBAAiB,SAAS,CAAC;AAAA,MACzF,OAAO;AACL,kCAA0B;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,uBAAuB;AAE9B,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC7C,WAAO,kBAAkB;AAAA,EAC3B;AACA,MAAI,qBAAqB,MAAM;AAE7B,UAAM,kBAAkB,SAAS,cAAc,KAAK;AACpD,UAAM,iBAAiB,gBAAgB;AACvC,oBAAgB,MAAM;AACtB,mBAAe,QAAQ;AACvB,mBAAe,WAAW;AAC1B,mBAAe,aAAa;AAC5B,mBAAe,gBAAgB;AAC/B,mBAAe,WAAW;AAC1B,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAM,eAAe,QAAQ;AAC7B,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AACtB,oBAAgB,YAAY,OAAO;AACnC,aAAS,KAAK,YAAY,eAAe;AACzC,wBAAoB,kBAAkB;AAItC,QAAI,gBAAgB,eAAe,GAAG;AAKpC,sBAAgB,aAAa;AAC7B,0BAAoB,gBAAgB,eAAe,IAAI,kBAAkB,UAAU,kBAAkB;AAAA,IACvG;AACA,oBAAgB,OAAO;AAAA,EACzB;AACA,SAAO;AACT;AACA,IAAI;AAEJ,SAAS,qBAAqB;AAC5B,MAAI,wBAAwB,MAAM;AAChC,UAAM,OAAO,OAAO,aAAa,cAAc,SAAS,OAAO;AAC/D,2BAAuB,CAAC,EAAE,SAAS,KAAK,oBAAoB,KAAK;AAAA,EACnE;AACA,SAAO;AACT;AAEA,SAAS,eAAe,SAAS;AAC/B,MAAI,mBAAmB,GAAG;AACxB,UAAM,WAAW,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAG/D,QAAI,OAAO,eAAe,eAAe,cAAc,oBAAoB,YAAY;AACrF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,oCAAoC;AAC3C,MAAI,gBAAgB,OAAO,aAAa,eAAe,WAAW,SAAS,gBAAgB;AAC3F,SAAO,iBAAiB,cAAc,YAAY;AAChD,UAAM,mBAAmB,cAAc,WAAW;AAClD,QAAI,qBAAqB,eAAe;AACtC;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAG9B,SAAO,MAAM,eAAe,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAC9D;AAGA,SAAS,qBAAqB;AAK5B;AAAA;AAAA,IAEE,OAAO,cAAc,eAAe,CAAC,CAAC;AAAA,IAEtC,OAAO,YAAY,eAAe,CAAC,CAAC;AAAA,IAEpC,OAAO,SAAS,eAAe,CAAC,CAAC;AAAA,IAEjC,OAAO,UAAU,eAAe,CAAC,CAAC;AAAA;AAEtC;;;ACzSA,SAAS,sBAAsB,OAAO;AACpC,SAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AACzC;AACA,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACtD,MAAI,eAAe,KAAK,GAAG;AACzB,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,SAAO,UAAU,WAAW,IAAI,gBAAgB;AAClD;AAKA,SAAS,eAAe,OAAO;AAI7B,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAC1D;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AAGA,SAAS,oBAAoB,OAAO;AAClC,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,UAAU,WAAW,QAAQ,GAAG,KAAK;AACrD;AAMA,SAAS,cAAc,cAAc;AACnC,SAAO,wBAAwB,aAAa,aAAa,gBAAgB;AAC3E;", "names": ["RtlScrollAxisType"]}