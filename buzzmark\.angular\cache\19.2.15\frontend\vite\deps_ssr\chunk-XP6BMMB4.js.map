{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/text-field.mjs"], "sourcesContent": ["import { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, Injectable, ElementRef, EventEmitter, Directive, Output, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { DOCUMENT } from '@angular/common';\nimport { auditTime, takeUntil } from 'rxjs/operators';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {\n  static ɵfac = function _CdkTextFieldStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkTextFieldStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _CdkTextFieldStyleLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-text-field-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _CdkTextFieldStyleLoader_Template(rf, ctx) {},\n    styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkTextFieldStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-text-field-style-loader': ''\n      },\n      styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\"]\n    }]\n  }], null, null);\n})();\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  _monitoredElements = new Map();\n  constructor() {}\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n    this._styleLoader.load(_CdkTextFieldStyleLoader);\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      return info.subject;\n    }\n    const result = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('animationstart', listener, listenerOptions);\n      element.classList.add('cdk-text-field-autofill-monitored');\n    });\n    this._monitoredElements.set(element, {\n      subject: result,\n      unlisten: () => {\n        element.removeEventListener('animationstart', listener, listenerOptions);\n      }\n    });\n    return result;\n  }\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  static ɵfac = function AutofillMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AutofillMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AutofillMonitor,\n    factory: AutofillMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n  _elementRef = inject(ElementRef);\n  _autofillMonitor = inject(AutofillMonitor);\n  /** Emits when the autofill state of the element changes. */\n  cdkAutofill = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n  static ɵfac = function CdkAutofill_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAutofill)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAutofill,\n    selectors: [[\"\", \"cdkAutofill\", \"\"]],\n    outputs: {\n      cdkAutofill: \"cdkAutofill\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]'\n    }]\n  }], () => [], {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n  _previousValue;\n  _initialHeight;\n  _destroyed = new Subject();\n  _minRows;\n  _maxRows;\n  _enabled = true;\n  /**\n   * Value of minRows as of last resize. If the minRows has decreased, the\n   * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n   * does not have the same problem because it does not affect the textarea's scrollHeight.\n   */\n  _previousMinRows = -1;\n  _textareaElement;\n  /** Minimum amount of rows in the textarea. */\n  get minRows() {\n    return this._minRows;\n  }\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n  get maxRows() {\n    return this._maxRows;\n  }\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n    this._cacheTextareaPlaceholderHeight();\n  }\n  /** Cached height of a textarea with a single row. */\n  _cachedLineHeight;\n  /** Cached height of a textarea with only the placeholder. */\n  _cachedPlaceholderHeight;\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  _hasFocus;\n  _isViewInited = false;\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_CdkTextFieldStyleLoader);\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        fromEvent(window, 'resize').pipe(auditTime(16), takeUntil(this._destroyed)).subscribe(() => this.resizeToFitContent(true));\n        this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n      });\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n  ngOnDestroy() {\n    this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n    this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    let textareaClone = this._textareaElement.cloneNode(false);\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = '';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    textareaClone.style.overflow = 'hidden';\n    this._textareaElement.parentNode.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n    return scrollHeight;\n  }\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n  /** Handles `focus` and `blur` events. */\n  _handleFocusEvent = event => {\n    this._hasFocus = event.type === 'focus';\n  };\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea;\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n    }\n  }\n  static ɵfac = function CdkTextareaAutosize_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTextareaAutosize)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTextareaAutosize,\n    selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n    hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n    hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n          return ctx._noopInputHandler();\n        });\n      }\n    },\n    inputs: {\n      minRows: [0, \"cdkAutosizeMinRows\", \"minRows\"],\n      maxRows: [0, \"cdkAutosizeMaxRows\", \"maxRows\"],\n      enabled: [2, \"cdkTextareaAutosize\", \"enabled\", booleanAttribute],\n      placeholder: \"placeholder\"\n    },\n    exportAs: [\"cdkTextareaAutosize\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      }\n    }]\n  }], () => [], {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTextareaAutosize',\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass TextFieldModule {\n  static ɵfac = function TextFieldModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TextFieldModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TextFieldModule,\n    imports: [CdkAutofill, CdkTextareaAutosize],\n    exports: [CdkAutofill, CdkTextareaAutosize]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,kBAA0C;AAE1C,uBAAqC;AAGrC,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,+BAA+B,EAAE;AAAA,IAC7C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC/D,QAAQ,CAAC,ymBAAymB;AAAA,IAClnB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,+BAA+B;AAAA,MACjC;AAAA,MACA,QAAQ,CAAC,ymBAAymB;AAAA,IACpnB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,kBAAkB,gCAAgC;AAAA,EACtD,SAAS;AACX,CAAC;AAMD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,eAAe,OAAO,sBAAsB;AAAA,EAC5C,qBAAqB,oBAAI,IAAI;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,QAAQ,cAAc;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,SAAK,aAAa,KAAK,wBAAwB;AAC/C,UAAM,UAAU,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,mBAAmB,IAAI,OAAO;AAChD,QAAI,MAAM;AACR,aAAO,KAAK;AAAA,IACd;AACA,UAAM,SAAS,IAAI,oBAAQ;AAC3B,UAAM,WAAW;AACjB,UAAM,WAAW,WAAS;AAIxB,UAAI,MAAM,kBAAkB,mCAAmC,CAAC,QAAQ,UAAU,SAAS,QAAQ,GAAG;AACpG,gBAAQ,UAAU,IAAI,QAAQ;AAC9B,aAAK,QAAQ,IAAI,MAAM,OAAO,KAAK;AAAA,UACjC,QAAQ,MAAM;AAAA,UACd,cAAc;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ,WAAW,MAAM,kBAAkB,iCAAiC,QAAQ,UAAU,SAAS,QAAQ,GAAG;AACxG,gBAAQ,UAAU,OAAO,QAAQ;AACjC,aAAK,QAAQ,IAAI,MAAM,OAAO,KAAK;AAAA,UACjC,QAAQ,MAAM;AAAA,UACd,cAAc;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAQ,iBAAiB,kBAAkB,UAAU,eAAe;AACpE,cAAQ,UAAU,IAAI,mCAAmC;AAAA,IAC3D,CAAC;AACD,SAAK,mBAAmB,IAAI,SAAS;AAAA,MACnC,SAAS;AAAA,MACT,UAAU,MAAM;AACd,gBAAQ,oBAAoB,kBAAkB,UAAU,eAAe;AAAA,MACzE;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,eAAe,cAAc;AAC3B,UAAM,UAAU,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,mBAAmB,IAAI,OAAO;AAChD,QAAI,MAAM;AACR,WAAK,SAAS;AACd,WAAK,QAAQ,SAAS;AACtB,cAAQ,UAAU,OAAO,mCAAmC;AAC5D,cAAQ,UAAU,OAAO,2BAA2B;AACpD,WAAK,mBAAmB,OAAO,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAClF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,mBAAmB,OAAO,eAAe;AAAA;AAAA,EAEzC,cAAc,IAAI,aAAa;AAAA,EAC/B,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,SAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,WAAS,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,EACjG;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,eAAe,KAAK,WAAW;AAAA,EACvD;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,SAAS;AAAA,MACP,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB;AAAA,EACA;AAAA,EACA,aAAa,IAAI,oBAAQ;AAAA,EACzB;AAAA,EACA;AAAA,EACA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,mBAAmB;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,qBAAqB,KAAK;AAC1C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,qBAAqB,KAAK;AAC1C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AAGjB,QAAI,KAAK,aAAa,OAAO;AAC3B,OAAC,KAAK,WAAW,SAAS,KAAK,mBAAmB,IAAI,IAAI,KAAK,MAAM;AAAA,IACvE;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,2BAA2B;AAChC,QAAI,OAAO;AACT,WAAK,iBAAiB,aAAa,eAAe,KAAK;AAAA,IACzD,OAAO;AACL,WAAK,iBAAiB,gBAAgB,aAAa;AAAA,IACrD;AACA,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA,gBAAgB;AAAA,EAChB,cAAc;AACZ,UAAM,cAAc,OAAO,sBAAsB;AACjD,gBAAY,KAAK,wBAAwB;AACzC,SAAK,mBAAmB,KAAK,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,YAAY,KAAK,WAAW,KAAK,oBAAoB,GAAG,KAAK,UAAU,KAAK,iBAAiB,OAAO;AAC1G,QAAI,WAAW;AACb,WAAK,iBAAiB,MAAM,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,YAAY,KAAK,WAAW,KAAK,oBAAoB,GAAG,KAAK,UAAU,KAAK,iBAAiB,OAAO;AAC1G,QAAI,WAAW;AACb,WAAK,iBAAiB,MAAM,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAE5B,WAAK,iBAAiB,KAAK,iBAAiB,MAAM;AAClD,WAAK,mBAAmB;AACxB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMA,UAAS,KAAK,WAAW;AAC/B,mCAAUA,SAAQ,QAAQ,EAAE,SAAK,4BAAU,EAAE,OAAG,4BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,IAAI,CAAC;AACzH,aAAK,iBAAiB,iBAAiB,SAAS,KAAK,iBAAiB;AACtE,aAAK,iBAAiB,iBAAiB,QAAQ,KAAK,iBAAiB;AAAA,MACvE,CAAC;AACD,WAAK,gBAAgB;AACrB,WAAK,mBAAmB,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,oBAAoB,SAAS,KAAK,iBAAiB;AACzE,SAAK,iBAAiB,oBAAoB,QAAQ,KAAK,iBAAiB;AACxE,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B;AACzB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AAEA,QAAI,gBAAgB,KAAK,iBAAiB,UAAU,KAAK;AACzD,kBAAc,OAAO;AAIrB,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,YAAY;AAMhC,kBAAc,MAAM,WAAW;AAC/B,SAAK,iBAAiB,WAAW,YAAY,aAAa;AAC1D,SAAK,oBAAoB,cAAc;AACvC,kBAAc,OAAO;AAErB,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,iBAAiB,QAAQ,MAAM,gBAAgB;AACrD,UAAM,YAAY,KAAK,UAAU;AACjC,UAAM,oBAAoB,aAAa,KAAK;AAC5C,UAAM,iBAAiB,YAAY,4CAA4C;AAI/E,QAAI,mBAAmB;AACrB,cAAQ,MAAM,eAAe,GAAG,QAAQ,YAAY;AAAA,IACtD;AAGA,YAAQ,UAAU,IAAI,cAAc;AAGpC,UAAM,eAAe,QAAQ,eAAe;AAC5C,YAAQ,UAAU,OAAO,cAAc;AACvC,QAAI,mBAAmB;AACrB,cAAQ,MAAM,eAAe;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,kCAAkC;AAChC,QAAI,CAAC,KAAK,iBAAiB,KAAK,4BAA4B,QAAW;AACrE;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,2BAA2B;AAChC;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,iBAAiB;AACpC,SAAK,iBAAiB,QAAQ,KAAK,iBAAiB;AACpD,SAAK,2BAA2B,KAAK,qBAAqB;AAC1D,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA;AAAA,EAEA,oBAAoB,WAAS;AAC3B,SAAK,YAAY,MAAM,SAAS;AAAA,EAClC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,QAAQ,OAAO;AAEhC,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,SAAK,yBAAyB;AAC9B,SAAK,gCAAgC;AAGrC,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AACA,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,QAAQ,SAAS;AAEvB,QAAI,CAAC,SAAS,KAAK,aAAa,KAAK,oBAAoB,UAAU,KAAK,gBAAgB;AACtF;AAAA,IACF;AACA,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,SAAS,KAAK,IAAI,cAAc,KAAK,4BAA4B,CAAC;AAExE,aAAS,MAAM,SAAS,GAAG,MAAM;AACjC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,OAAO,0BAA0B,aAAa;AAChD,8BAAsB,MAAM,KAAK,uBAAuB,QAAQ,CAAC;AAAA,MACnE,OAAO;AACL,mBAAW,MAAM,KAAK,uBAAuB,QAAQ,CAAC;AAAA,MACxD;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AAGN,QAAI,KAAK,mBAAmB,QAAW;AACrC,WAAK,iBAAiB,MAAM,SAAS,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,EAEpB;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,UAAU;AAC/B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAOJ,QAAI,CAAC,KAAK,WAAW,aAAa,KAAK,WAAW;AAChD,eAAS,kBAAkB,gBAAgB,YAAY;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,uBAAuB,EAAE,CAAC;AAAA,IACnD,WAAW,CAAC,QAAQ,KAAK,GAAG,uBAAuB;AAAA,IACnD,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,+CAA+C;AAC7E,iBAAO,IAAI,kBAAkB;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,sBAAsB,SAAS;AAAA,MAC5C,SAAS,CAAC,GAAG,sBAAsB,SAAS;AAAA,MAC5C,SAAS,CAAC,GAAG,uBAAuB,WAAW,gBAAgB;AAAA,MAC/D,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAC,qBAAqB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,mBAAmB;AAAA,IAC1C,SAAS,CAAC,aAAa,mBAAmB;AAAA,EAC5C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,mBAAmB;AAAA,MAC1C,SAAS,CAAC,aAAa,mBAAmB;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["window"]}