/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["fa-AF", [["ق", "ب"], ["ق.ظ.", "ب.ظ."], ["قبل‌ازظهر", "بعدازظهر"]], u, [["ی", "د", "س", "چ", "پ", "ج", "ش"], ["یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنجشنبه", "جمعه", "شنبه"], u, ["۱ش", "۲ش", "۳ش", "۴ش", "۵ش", "ج", "ش"]], u, [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "ا", "ن", "د"], ["جنو", "فبروری", "مارچ", "اپریل", "می", "جون", "جول", "اگست", "سپتمبر", "اکتوبر", "نومبر", "دسم"], ["جنوری", "فبروری", "مارچ", "اپریل", "می", "جون", "جولای", "اگست", "سپتمبر", "اکتوبر", "نومبر", "دسمبر"]], [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "ا", "ن", "د"], ["جنوری", "فبروری", "مارچ", "اپریل", "می", "جون", "جولای", "اگست", "سپتمبر", "اکتوبر", "نومبر", "دسمبر"], u], [["ق", "م"], ["ق.م.", "م."], ["قبل از میلاد", "میلادی"]], 6, [4, 5], ["y/M/d", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["H:mm", "H:mm:ss", "H:mm:ss (z)", "H:mm:ss (zzzz)"], ["{1}،‏ {0}", u, "{1}، ساعت {0}", u], [".", ",", ";", "%", "‎+", "‎−", "E", "×", "‰", "∞", "ناعدد", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "AFN", "؋", "افغانی افغانستان", { "AFN": ["؋"], "BYN": [u, "р."], "CAD": ["$CA", "$"], "CNY": ["¥CN", "¥"], "HKD": ["$HK", "$"], "IRR": ["ریال"], "MXN": ["$MX", "$"], "NZD": ["$NZ", "$"], "PHP": [u, "₱"], "THB": ["฿"], "XCD": ["$EC", "$"], "XOF": ["فرانک CFA"] }, "rtl", plural];
//# sourceMappingURL=fa-AF.js.map