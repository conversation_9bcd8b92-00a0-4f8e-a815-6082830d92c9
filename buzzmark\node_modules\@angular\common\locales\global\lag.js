/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val, i = Math.floor(Math.abs(val));

if (n === 0)
    return 0;
if ((i === 0 || i === 1) && !(n === 0))
    return 1;
return 5;
}
    global.ng.common.locales['lag'] = ["lag",[["TOO","MUU"],u,u],u,[["P","T","E","O","A","I","M"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON>atu","<PERSON>ne","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON>j<PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON>atu","<PERSON>ne","<PERSON><PERSON>ano","<PERSON>h","Ijm","<PERSON>óosi"]],u,[["<PERSON>","N","<PERSON>","I","I","I","<PERSON>","V","S","I","S","<PERSON>"],["<PERSON>úngatɨ","Naanɨ","<PERSON>enda","Ikúmi","Inyambala","Idwaata","Mʉʉnchɨ","Vɨɨrɨ","Saatʉ","Inyi","Saano","Sasatʉ"],["Kʉfúngatɨ","Kʉnaanɨ","Kʉkeenda","Kwiikumi","Kwiinyambála","Kwiidwaata","Kʉmʉʉnchɨ","Kʉvɨɨrɨ","Kʉsaatʉ","Kwiinyi","Kʉsaano","Kʉsasatʉ"]],u,[["KSA","KA"],u,["Kɨrɨsitʉ sɨ anavyaal","Kɨrɨsitʉ akavyaalwe"]],1,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"TZS","TSh","Shilíingi ya Taansanía",{"JPY":["JP¥","¥"],"TZS":["TSh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    