/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["gsw-LI", [["vorm.", "nam."], u, ["am Vormittag", "am Namittag"]], [["vorm.", "nam."], u, ["Vormittag", "Namittag"]], [["S", "M", "D", "M", "D", "F", "S"], ["Su.", "Mä.", "Zi.", "Mi.", "Du.", "Fr.", "Sa."], ["Sunntig", "<PERSON><PERSON><PERSON>ntig", "<PERSON>iischtig", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ti<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["<PERSON>.", "<PERSON><PERSON>.", "Zi.", "<PERSON>.", "<PERSON>.", "<PERSON>.", "Sa."]], u, [["J", "F", "M", "A", "M", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "N", "<PERSON>"], ["<PERSON>", "Feb", "<PERSON><PERSON>r", "Apr", "<PERSON>", "<PERSON>", "<PERSON>", "Aug", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>z"], ["<PERSON>uar", "<PERSON>ruar", "<PERSON><PERSON>rz", "April", "<PERSON>", "<PERSON>i", "<PERSON>i", "Auguscht", "Sept<PERSON>mber", "Oktoober", "Novämber", "Dezämber"]], u, [["v. Chr.", "n. Chr."], u, u], 1, [6, 0], ["dd.MM.yy", "dd.MM.y", "d. MMMM y", "EEEE, d. MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", "’", ";", "%", "+", "−", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "CHF", "CHF", "Schwiizer Franke", { "ATS": ["öS"] }, "ltr", plural];
//# sourceMappingURL=gsw-LI.js.map