/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["jgo", [["mbaꞌmbaꞌ", "ŋka mbɔ́t nji"], u, u], u, [["Sɔ<PERSON>", "<PERSON>ɔ<PERSON>", "Á<PERSON>", "Wɛ́", "Tɔ́", "Fɛ", "Sá"], ["Sɔ́ndi", "<PERSON>ɔ́ndi", "<PERSON><PERSON> Mɔ́ndi", "Wɛ́nɛsɛdɛ", "Tɔ́sɛdɛ", "Fɛlâyɛdɛ", "Sásidɛ"], u, u], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Pɛ́tá<PERSON>", "<PERSON><PERSON>sa<PERSON>́<PERSON>́k<PERSON>", "Pɛsaŋ Pataa", "Pɛsaŋ Pɛ́nɛ́ntúkú", "Pɛsaŋ Saambá", "Pɛsaŋ Pɛ́nɛ́fɔm", "Pɛsaŋ Pɛ́nɛ́pfúꞋú", "Pɛsaŋ Nɛgɛ́m", "Pɛsaŋ Ntsɔ̌pmɔ́", "Pɛsaŋ Ntsɔ̌ppá"], u], u, [["BCE", "CE"], u, ["tsɛttsɛt mɛŋguꞌ mi ɛ́ lɛɛnɛ Kɛlísɛtɔ gɔ ńɔ́", "tsɛttsɛt mɛŋguꞌ mi ɛ́ fúnɛ Kɛlísɛtɔ tɔ́ mɔ́"]], 1, [6, 0], ["y-MM-dd", "y MMM d", "y MMMM d", "EEEE, y MMMM dd"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "XAF", "FCFA", "Fɛlâŋ", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=jgo.js.map