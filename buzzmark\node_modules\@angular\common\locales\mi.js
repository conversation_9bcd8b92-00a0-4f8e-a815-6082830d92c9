/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["mi", [["AM", "PM"], u, u], u, [["T", "H", "T", "A", "P", "M", "H"], ["Tap", "Hin", "Tū", "A<PERSON>", "<PERSON>r", "Mer", "Hor"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["Tap", "Hin", "<PERSON>ū", "Apa", "Par", "Mer", "Hor"]], u, [["K", "H", "P", "P", "H", "P", "H", "H", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>uku", "Rangi", "<PERSON>ki"], ["Kohitātea", "Huitanguru", "Poutūterangi", "Paengawhāwhā", "Haratua", "Pipiri", "Hōngongoi", "Hereturikōkā", "Mahuru", "Whiringa-ā-nuku", "Whiringa-ā-rangi", "Hakihea"]], u, [["BCE", "CE"], u, u], 1, [6, 0], ["dd-MM-y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "NZD", "$", "Tāra o Aotearoa", { "NZD": ["$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=mi.js.map