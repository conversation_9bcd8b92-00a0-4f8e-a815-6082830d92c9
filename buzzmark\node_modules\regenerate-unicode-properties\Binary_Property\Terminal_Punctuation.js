const set = require('regenerate')(0x21, 0x2C, 0x2E, 0x3F, 0x37E, 0x387, 0x589, 0x5C3, 0x60C, 0x61B, 0x6D4, 0x70C, 0x85E, 0xF08, 0x166E, 0x17DA, 0x2024, 0x2E2E, 0x2E3C, 0x2E41, 0x2E4C, 0xA92F, 0xAADF, 0xABEB, 0xFE12, 0xFF01, 0xFF0C, 0xFF0E, 0xFF1F, 0xFF61, 0xFF64, 0x1039F, 0x103D0, 0x10857, 0x1091F, 0x111CD, 0x112A9, 0x11944, 0x11946, 0x11C71, 0x16AF5, 0x16B44, 0x1BC9F);
set.addRange(0x3A, 0x3B).addRange(0x61D, 0x61F).addRange(0x700, 0x70A).addRange(0x7F8, 0x7F9).addRange(0x830, 0x835).addRange(0x837, 0x83E).addRange(0x964, 0x965).addRange(0xE5A, 0xE5B).addRange(0xF0D, 0xF12).addRange(0x104A, 0x104B).addRange(0x1361, 0x1368).addRange(0x16EB, 0x16ED).addRange(0x1735, 0x1736).addRange(0x17D4, 0x17D6).addRange(0x1802, 0x1805).addRange(0x1808, 0x1809).addRange(0x1944, 0x1945).addRange(0x1AA8, 0x1AAB).addRange(0x1B4E, 0x1B4F).addRange(0x1B5A, 0x1B5B).addRange(0x1B5D, 0x1B5F).addRange(0x1B7D, 0x1B7F).addRange(0x1C3B, 0x1C3F).addRange(0x1C7E, 0x1C7F).addRange(0x203C, 0x203D).addRange(0x2047, 0x2049).addRange(0x2CF9, 0x2CFB).addRange(0x2E4E, 0x2E4F).addRange(0x2E53, 0x2E54).addRange(0x3001, 0x3002).addRange(0xA4FE, 0xA4FF).addRange(0xA60D, 0xA60F).addRange(0xA6F3, 0xA6F7).addRange(0xA876, 0xA877).addRange(0xA8CE, 0xA8CF).addRange(0xA9C7, 0xA9C9).addRange(0xAA5D, 0xAA5F).addRange(0xAAF0, 0xAAF1).addRange(0xFE15, 0xFE16).addRange(0xFE50, 0xFE52).addRange(0xFE54, 0xFE57).addRange(0xFF1A, 0xFF1B).addRange(0x10A56, 0x10A57).addRange(0x10AF0, 0x10AF5).addRange(0x10B3A, 0x10B3F).addRange(0x10B99, 0x10B9C).addRange(0x10F55, 0x10F59).addRange(0x10F86, 0x10F89).addRange(0x11047, 0x1104D).addRange(0x110BE, 0x110C1).addRange(0x11141, 0x11143);
set.addRange(0x111C5, 0x111C6).addRange(0x111DE, 0x111DF).addRange(0x11238, 0x1123C).addRange(0x113D4, 0x113D5).addRange(0x1144B, 0x1144D).addRange(0x1145A, 0x1145B).addRange(0x115C2, 0x115C5).addRange(0x115C9, 0x115D7).addRange(0x11641, 0x11642).addRange(0x1173C, 0x1173E).addRange(0x11A42, 0x11A43).addRange(0x11A9B, 0x11A9C).addRange(0x11AA1, 0x11AA2).addRange(0x11C41, 0x11C43).addRange(0x11EF7, 0x11EF8).addRange(0x11F43, 0x11F44).addRange(0x12470, 0x12474).addRange(0x16A6E, 0x16A6F).addRange(0x16B37, 0x16B39).addRange(0x16D6E, 0x16D6F).addRange(0x16E97, 0x16E98).addRange(0x1DA87, 0x1DA8A);
exports.characters = set;
