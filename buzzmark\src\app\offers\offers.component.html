<div class="p-6">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-3xl font-bold text-buzzmark-blue dark:text-blue-400">Mes offres</h2>
    <button mat-raised-button color="primary" class="bg-buzzmark-orange hover:bg-orange-500 text-white" (click)="createNewCampaign()">Nouvelle campagne</button>
  </div>

  <!-- Container de recherche et filtres -->
  <div class="mb-6 flex space-x-4">
    <mat-form-field class="w-1/3">
      <mat-label>Rechercher</mat-label>
      <input matInput [(ngModel)]="searchQuery" (ngModelChange)="onFilterChange()" placeholder="Nom de campagne...">
    </mat-form-field>
    <mat-form-field class="w-1/3">
      <mat-label>Trier par</mat-label>
      <mat-select [(ngModel)]="sortBy" (ngModelChange)="onFilterChange()">
        <mat-option value="date">Date</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="w-1/3">
      <mat-label>Statut</mat-label>
      <mat-select [(ngModel)]="statusFilter" (ngModelChange)="onFilterChange()">
        <mat-option *ngFor="let status of statuses" [value]="status">
          {{ status === 'all' ? 'Tous les statuts' : status | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <!-- Liste des campagnes -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let offer of offers" class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h3 class="text-xl font-semibold text-buzzmark-blue dark:text-blue-400">{{ offer.name }}</h3>
      <p class="text-gray-600 dark:text-gray-300">Budget: {{ offer.budget }} TND</p>
      <div class="mt-2 space-y-1 text-sm text-gray-700 dark:text-gray-300">
        <p>Vues: {{ offer.views }}</p>
        <p>Partages: {{ offer.shares }}</p>
        <p>Interactions: {{ offer.interactions }}</p>
      </div>
      <p class="mt-2 text-sm text-gray-500">Date limite: {{ offer.deadline }}</p>
      <div class="mt-4 flex space-x-2">
        <button mat-raised-button color="primary" class="bg-buzzmark-blue hover:bg-blue-500 text-white">Modifier</button>
        <button mat-raised-button color="warn" class="bg-yellow-500 hover:bg-yellow-600 text-white">Mettre en pause</button>
        <button mat-raised-button color="warn" class="bg-red-500 hover:bg-red-600 text-white">Terminer</button>
        <button mat-raised-button color="primary" class="bg-buzzmark-orange hover:bg-orange-500 text-white">Voir les postulants</button>
      </div>
    </div>
  </div>

  <!-- Formulaire de création de nouvelle campagne -->
  <div *ngIf="showCreateForm" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-full max-w-md">
      <h3 class="text-2xl font-bold text-buzzmark-blue dark:text-blue-400 mb-4">Nouvelle campagne</h3>
      <form (ngSubmit)="onSubmitNewOffer()" class="space-y-4">
        <mat-form-field class="w-full">
          <mat-label>Nom de la campagne</mat-label>
          <input matInput [(ngModel)]="newOffer.name" name="name" required>
        </mat-form-field>
        <mat-form-field class="w-full">
          <mat-label>Budget (TND)</mat-label>
          <input matInput type="number" [(ngModel)]="newOffer.budget" name="budget" required step="0.01">
        </mat-form-field>
        <mat-form-field class="w-full">
          <mat-label>Date limite</mat-label>
          <input matInput [matDatepicker]="picker" [(ngModel)]="newOffer.deadline" name="deadline" required>
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
        <mat-form-field class="w-full">
          <mat-label>Statut</mat-label>
          <mat-select [(ngModel)]="newOffer.status" name="status">
            <mat-option value="pending">En attente</mat-option>
            <mat-option value="active">Actif</mat-option>
          </mat-select>
        </mat-form-field>
        <div class="flex space-x-2">
          <button mat-raised-button color="primary" type="submit" class="bg-buzzmark-blue hover:bg-blue-500 text-white">Créer</button>
          <button mat-raised-button (click)="cancelCreate()" class="bg-gray-500 hover:bg-gray-600 text-white">Annuler</button>
        </div>
      </form>
    </div>
  </div>
</div>